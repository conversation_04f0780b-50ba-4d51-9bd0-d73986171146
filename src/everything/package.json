{"name": "@modelcontextprotocol/server-everything", "version": "0.6.2", "description": "MCP server that exercises all the features of the MCP protocol", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/servers/issues", "type": "module", "bin": {"mcp-server-everything": "dist/index.js"}, "files": ["dist"], "scripts": {"build": "tsc && shx cp instructions.md dist/ && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsc --watch", "start": "node dist/index.js", "start:sse": "node dist/sse.js", "start:streamableHttp": "node dist/streamableHttp.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "express": "^4.21.1", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.5"}, "devDependencies": {"@types/express": "^5.0.0", "shx": "^0.3.4", "typescript": "^5.6.2"}}